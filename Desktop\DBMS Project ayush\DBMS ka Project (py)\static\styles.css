/* Import Google Fonts - Enhanced Typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap');

/* Global Reset and Base Styles */
*, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    letter-spacing: -0.01em;
    transition: background-color var(--transition-normal) var(--easing-standard),
                color var(--transition-normal) var(--easing-standard);
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 0.5em;
    color: var(--text-primary);
}

h1 { font-size: 2.5rem; letter-spacing: -0.02em; }
h2 { font-size: 2rem; letter-spacing: -0.015em; }
h3 { font-size: 1.5rem; letter-spacing: -0.01em; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: all var(--transition-fast) var(--easing-standard);
}

a:hover {
    color: var(--primary-dark);
}

img {
    max-width: 100%;
    height: auto;
}

/* Selection styling */
::selection {
    background-color: var(--primary-color);
    color: var(--text-light);
}

/* Root Variables - Light Theme (default) */
:root {
    /* Color Scheme - Teal & Coral Palette */
    --primary-color: #00897B;
    --primary-light: #4DB6AC;
    --primary-dark: #004D40;
    --accent-color: #FF7043;
    --success-color: #43A047;
    --warning-color: #FFB74D;
    --danger-color: #E53935;
    --info-color: #29B6F6;

    /* Gradient Definitions - Enhanced */
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    --gradient-accent: linear-gradient(135deg, var(--accent-color), #F4511E);
    --gradient-success: linear-gradient(135deg, var(--success-color), #2E7D32);
    --gradient-danger: linear-gradient(135deg, var(--danger-color), #C62828);
    --gradient-info: linear-gradient(135deg, var(--info-color), #0288D1);
    --gradient-warning: linear-gradient(135deg, var(--warning-color), #FF9800);

    /* Gradient Backgrounds - Enhanced */
    --gradient-bg-primary: linear-gradient(to right, rgba(0, 137, 123, 0.08), rgba(0, 77, 64, 0.03));
    --gradient-bg-success: linear-gradient(to right, rgba(67, 160, 71, 0.08), rgba(46, 125, 50, 0.03));
    --gradient-bg-danger: linear-gradient(to right, rgba(229, 57, 53, 0.08), rgba(198, 40, 40, 0.03));
    --gradient-bg-info: linear-gradient(to right, rgba(41, 182, 246, 0.08), rgba(2, 136, 209, 0.03));

    /* UI Elements - Enhanced */
    --background: #ECEFF1;
    --surface: #ffffff;
    --card-bg: #ffffff;
    --card-shadow: 0 10px 20px rgba(0, 137, 123, 0.08);
    --card-shadow-hover: 0 15px 30px rgba(0, 137, 123, 0.12);
    --subtle-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

    /* Glass Effect - Enhanced */
    --glass-bg: rgba(255, 255, 255, 0.85);
    --glass-border: rgba(0, 137, 123, 0.2);
    --glass-shadow: 0 10px 30px rgba(0, 137, 123, 0.15);
    --glass-blur: blur(12px);

    /* Text */
    --text-primary: #263238;
    --text-secondary: #546E7A;
    --text-tertiary: #78909C;
    --text-light: #ffffff;

    /* Borders */
    --border-color: #CFD8DC;
    --border-radius-sm: 8px;
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 24px;
    --border-radius-circle: 50%;
    --btn-hover: #00796B;

    /* Transitions */
    --transition-fast: 0.2s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
    --easing-standard: cubic-bezier(0.4, 0, 0.2, 1);
    --easing-decelerate: cubic-bezier(0.0, 0.0, 0.2, 1);
    --easing-accelerate: cubic-bezier(0.4, 0.0, 1, 1);
}

/* Dark Theme Variables */
.dark-theme {
    /* Color Scheme - Dark Mode Teal & Coral */
    --primary-color: #26A69A;
    --primary-light: #80CBC4;
    --primary-dark: #00796B;
    --accent-color: #FF8A65;
    --success-color: #66BB6A;
    --warning-color: #FFA726;
    --danger-color: #EF5350;
    --info-color: #42A5F5;

    /* Gradient Definitions - Darker */
    --gradient-primary: linear-gradient(135deg, #26A69A, #00695C);
    --gradient-accent: linear-gradient(135deg, #FF8A65, #E64A19);
    --gradient-success: linear-gradient(135deg, #66BB6A, #388E3C);
    --gradient-danger: linear-gradient(135deg, #EF5350, #D32F2F);
    --gradient-info: linear-gradient(135deg, #42A5F5, #1976D2);
    --gradient-warning: linear-gradient(135deg, #FFA726, #F57C00);

    /* Gradient Backgrounds */
    --gradient-bg-primary: linear-gradient(to right, rgba(38, 166, 154, 0.15), rgba(0, 105, 92, 0.08));
    --gradient-bg-success: linear-gradient(to right, rgba(102, 187, 106, 0.15), rgba(56, 142, 60, 0.08));
    --gradient-bg-danger: linear-gradient(to right, rgba(239, 83, 80, 0.15), rgba(211, 47, 47, 0.08));
    --gradient-bg-info: linear-gradient(to right, rgba(66, 165, 245, 0.15), rgba(25, 118, 210, 0.08));

    /* UI Elements */
    --background: #263238;
    --surface: #37474F;
    --card-bg: #455A64;
    --card-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    --card-shadow-hover: 0 12px 24px rgba(0, 77, 64, 0.4);
    --subtle-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

    /* Glass Effect - Dark */
    --glass-bg: rgba(38, 50, 56, 0.8);
    --glass-border: rgba(38, 166, 154, 0.15);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

    /* Text */
    --text-primary: #ECEFF1;
    --text-secondary: #B0BEC5;
    --text-tertiary: #78909C;

    /* Borders */
    --border-color: #546E7A;
    --btn-hover: #26A69A;
}

/* Body Styling */
body {
    background-color: var(--background);
    color: var(--text-primary);
    text-align: center;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 100vh;
    line-height: 1.6;
}

/* Theme Toggle */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: var(--card-bg);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--card-shadow);
    border: none;
    color: var(--primary-color);
    transition: transform 0.3s ease;
}

.theme-toggle:hover {
    transform: rotate(30deg);
}

/* Sidebar Navigation - Simplified */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 220px;
    height: 100vh;
    background: var(--surface);
    box-shadow: var(--card-shadow);
    display: flex;
    flex-direction: column;
    z-index: 1000;
    transition: transform var(--transition-fast) var(--easing-standard);
    overflow: hidden;
}

/* Sidebar Header */
.sidebar-header {
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-bottom: 1px solid var(--border-color);
}

.app-logo {
    width: 36px;
    height: 36px;
    border-radius: var(--border-radius);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.app-name {
    font-size: 16px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

/* Sidebar Menu */
.sidebar-menu {
    flex: 1;
    padding: 16px 0;
    display: flex;
    flex-direction: column;
    gap: 4px;
    overflow-y: auto;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-fast) var(--easing-standard);
    position: relative;
    gap: 12px;
    margin: 0 4px;
    border-radius: var(--border-radius);
}

.menu-item:hover {
    background: rgba(67, 97, 238, 0.08);
    color: var(--primary-color);
}

.menu-item.active {
    color: var(--text-light);
    background: var(--gradient-primary);
    font-weight: 600;
}

.menu-icon {
    width: 32px;
    height: 32px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    background: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
}

.menu-item.active .menu-icon {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-light);
}

.menu-text {
    font-size: 14px;
    font-weight: 500;
}

/* Logout Item */
.menu-item.logout {
    margin-top: 8px;
    color: var(--danger-color);
}

.menu-item.logout:hover {
    background: rgba(239, 71, 111, 0.08);
}

.menu-item.logout.active {
    background: var(--gradient-danger);
}

.menu-item.logout .menu-icon {
    background: rgba(239, 71, 111, 0.1);
    color: var(--danger-color);
}

.menu-item.logout.active .menu-icon {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-light);
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 12px 16px;
    border-top: 1px solid var(--border-color);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 6px;
    border-radius: var(--border-radius);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: var(--border-radius-circle);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.user-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Mobile Header - Enhanced */
.mobile-header {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 70px;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    box-shadow: var(--glass-shadow);
    border-bottom: 1px solid var(--glass-border);
    padding: 0 20px;
    align-items: center;
    justify-content: space-between;
    z-index: 999;
}

.menu-toggle {
    background: rgba(67, 97, 238, 0.08);
    border: none;
    color: var(--primary-color);
    font-size: 20px;
    cursor: pointer;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    transition: all var(--transition-fast) var(--easing-standard);
    box-shadow: var(--subtle-shadow);
    position: relative;
    overflow: hidden;
}

.menu-toggle::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(rgba(255, 255, 255, 0.1), transparent);
    border-radius: inherit;
}

.menu-toggle i {
    position: relative;
    z-index: 1;
}

.menu-toggle:hover {
    background: var(--primary-color);
    color: var(--text-light);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(67, 97, 238, 0.2);
}

.menu-toggle:active {
    transform: translateY(0);
}

.mobile-logo {
    font-size: 20px;
    font-weight: 700;
    color: var(--primary-color);
    font-family: 'Montserrat', sans-serif;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
}

.mobile-actions {
    display: flex;
    gap: 12px;
}

/* Theme Toggle Buttons - Enhanced */
.theme-toggle, .theme-toggle-mobile {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    font-size: 18px;
    cursor: pointer;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-circle);
    transition: all var(--transition-fast) var(--easing-standard);
    box-shadow: var(--glass-shadow);
    position: relative;
    overflow: hidden;
    z-index: 100;
}

.theme-toggle::after, .theme-toggle-mobile::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(rgba(255, 255, 255, 0.1), transparent);
    border-radius: inherit;
}

.theme-toggle i, .theme-toggle-mobile i {
    position: relative;
    z-index: 1;
    transition: transform var(--transition-normal) var(--easing-standard);
}

.theme-toggle:hover, .theme-toggle-mobile:hover {
    background: var(--primary-color);
    color: var(--text-light);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(67, 97, 238, 0.2);
}

.theme-toggle:hover i, .theme-toggle-mobile:hover i {
    transform: rotate(30deg);
}

.theme-toggle:active, .theme-toggle-mobile:active {
    transform: translateY(0);
}

.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
}

/* Main Content Adjustment for Sidebar */
body {
    padding-left: 220px;
    padding-top: 0;
}

.dashboard-container {
    padding-top: 16px;
    padding-bottom: 16px;
}

/* Profile Section */
.profile-container {
    width: 100%;
    max-width: 1200px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: var(--surface);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: 30px;
}

.profile-container h1 {
    font-size: 26px;
    font-weight: 600;
    color: var(--primary-color);
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--text-light);
}

.btn-primary:hover {
    background: var(--btn-hover);
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--text-light);
}

.logout {
    background: var(--primary-color);
    padding: 10px 20px;
    border-radius: var(--border-radius);
    color: white;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.logout:hover {
    background: var(--btn-hover);
    transform: translateY(-2px);
}

/* Main Content */
.main-container {
    width: 100%;
    max-width: 1200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 20px;
}

/* Flash Messages */
.flashes {
    list-style: none;
    padding: 0;
    margin: 20px 0;
    width: 100%;
    max-width: 600px;
}

.flashes li {
    padding: 15px;
    border-radius: var(--border-radius);
    font-size: 14px;
    margin-bottom: 10px;
    box-shadow: var(--card-shadow);
    animation: slideIn 0.3s ease-out forwards;
    opacity: 0;
    transform: translateY(-20px);
}

@keyframes slideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.success {
    background: var(--success-color);
    color: white;
}

.danger, .error {
    background: var(--danger-color);
    color: white;
}

.warning {
    background: var(--warning-color);
    color: white;
}

/* Form Styling */
form {
    background: var(--card-bg);
    padding: 30px;
    border-radius: var(--border-radius);
    width: 100%;
    max-width: 500px;
    box-shadow: var(--card-shadow);
    text-align: left;
    margin-bottom: 30px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

form:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-primary);
}

input, select, textarea {
    width: 100%;
    padding: 12px 15px;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    background: var(--surface);
    color: var(--text-primary);
    font-size: 16px;
    transition: all 0.3s ease;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

.password-field {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    font-size: 16px;
}

/* Button Styling */
button, .btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    border: none;
    font-size: 16px;
}

button.btn-primary, .btn-primary {
    background-color: var(--primary-color);
    color: var(--text-light);
}

button:hover, .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

button.btn-primary:hover, .btn-primary:hover {
    background-color: var(--btn-hover);
}

.form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
}

/* Controls Container - Enhanced with Gradients */
.controls-container {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 16px;
    width: 100%;
    margin-bottom: 16px;
    transition: all var(--transition-fast) var(--easing-standard);
    position: relative;
    overflow: hidden;
}

.controls-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
}

.controls-container:hover {
    box-shadow: var(--card-shadow-hover);
}

.controls-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
    gap: 15px;
}

.controls-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
}

.controls-header h3 i {
    color: var(--primary-color);
}

.view-all-btn {
    background: var(--gradient-primary);
    border: none;
    color: var(--text-light);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast) var(--easing-standard);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    font-size: 14px;
    box-shadow: 0 4px 10px rgba(67, 97, 238, 0.15);
    margin-left: auto;
    position: relative;
    overflow: hidden;
}

.view-all-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(255, 255, 255, 0.1), transparent);
    pointer-events: none;
}

.btn-icon {
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    transition: all var(--transition-fast) var(--easing-standard);
}

.btn-text {
    padding: 8px 12px;
    transition: all var(--transition-fast) var(--easing-standard);
}

.view-all-btn:hover {
    box-shadow: 0 6px 15px rgba(67, 97, 238, 0.25);
    transform: translateY(-2px);
}

.view-all-btn:hover .btn-icon {
    background: rgba(255, 255, 255, 0.3);
}

.view-all-btn:active {
    transform: translateY(0);
    box-shadow: 0 4px 8px rgba(67, 97, 238, 0.2);
}

/* Filter Controls - Modern Button Layout */
.filter-controls {
    display: flex;
    flex-direction: column;
    background: var(--gradient-bg-primary);
    padding: 20px;
    border-radius: var(--border-radius);
    margin-top: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all var(--transition-normal) var(--easing-standard);
    border: 1px solid rgba(67, 97, 238, 0.1);
}

.filter-controls:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.filter-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 15px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-title i {
    color: var(--primary-color);
}

.filter-options {
    width: 100%;
}

.filter-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%;
}

@media (min-width: 768px) {
    .filter-form {
        flex-direction: row;
        flex-wrap: wrap;
        align-items: flex-end;
    }

    .filter-buttons, .sort-buttons {
        flex: 1;
        min-width: 200px;
    }

    .apply-btn {
        margin-left: auto;
    }
}

.filter-buttons, .sort-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 14px;
    margin-bottom: 4px;
}

.button-group {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.filter-radio {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.filter-btn-option {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 18px;
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast) var(--easing-standard);
    position: relative;
    overflow: hidden;
}

.filter-btn-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(255, 255, 255, 0.1), transparent);
    pointer-events: none;
    opacity: 0;
    transition: opacity var(--transition-fast) var(--easing-standard);
}

.filter-btn-option:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.filter-btn-option:hover::before {
    opacity: 1;
}

.filter-radio:checked + .filter-btn-option {
    background: var(--gradient-primary);
    color: var(--text-light);
    border-color: transparent;
    box-shadow: 0 4px 10px rgba(67, 97, 238, 0.25);
    transform: translateY(-1px);
}

.filter-btn-option.income:hover {
    border-color: var(--success-color);
    color: var(--success-color);
}

.filter-btn-option.expense:hover {
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.filter-radio:checked + .filter-btn-option.income {
    background: var(--gradient-success);
}

.filter-radio:checked + .filter-btn-option.expense {
    background: var(--gradient-danger);
}

.apply-btn {
    background: var(--gradient-primary);
    color: var(--text-light);
    border: none;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    font-size: 14px;
    box-shadow: 0 4px 10px rgba(67, 97, 238, 0.2);
    transition: all var(--transition-fast) var(--easing-standard);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 12px;
    align-self: flex-end;
    position: relative;
    overflow: hidden;
}

.apply-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(255, 255, 255, 0.1), transparent);
    pointer-events: none;
}

.apply-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(67, 97, 238, 0.3);
}

.apply-btn:active {
    transform: translateY(0);
    box-shadow: 0 4px 8px rgba(67, 97, 238, 0.2);
}

.apply-btn i {
    transition: transform var(--transition-fast) var(--easing-standard);
}

.apply-btn:hover i {
    transform: scale(1.1);
}

/* Transaction Cards - Simplified */
.transactions-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
}

.transaction-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 16px;
    transition: all var(--transition-fast) var(--easing-standard);
    display: flex;
    align-items: center;
    gap: 16px;
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
    backface-visibility: hidden;
}

.transaction-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(255, 255, 255, 0.05), transparent);
    pointer-events: none;
}

.transaction-card:hover {
    box-shadow: var(--card-shadow-hover);
    transform: translateY(-3px);
}

.transaction-card.income {
    border-left: 4px solid var(--success-color);
}

.transaction-card.expense {
    border-left: 4px solid var(--danger-color);
}

.transaction-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal) var(--easing-standard);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.transaction-icon::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(255, 255, 255, 0.2), transparent);
    pointer-events: none;
}

.income .transaction-icon {
    background: var(--gradient-success);
    color: white;
}

.expense .transaction-icon {
    background: var(--gradient-danger);
    color: white;
}

.transaction-card:hover .transaction-icon {
    transform: scale(1.05);
}

.transaction-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.transaction-primary {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.transaction-category {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 15px;
}

.transaction-amount {
    font-size: 16px;
    font-weight: 700;
}

.income .transaction-amount {
    color: var(--success-color);
}

.expense .transaction-amount {
    color: var(--danger-color);
}

.transaction-secondary {
    display: flex;
    gap: 12px;
    color: var(--text-secondary);
    font-size: 12px;
}

.transaction-date, .transaction-time {
    display: flex;
    align-items: center;
    gap: 4px;
}

.transaction-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    background: var(--surface);
    transition: all var(--transition-fast) var(--easing-standard);
    text-decoration: none;
    font-size: 14px;
}

.edit-btn:hover {
    background: var(--primary-color);
    color: var(--text-light);
}

.delete-btn:hover {
    background: var(--danger-color);
    color: var(--text-light);
}

/* No Transactions State */
.no-transactions {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 40px 20px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.no-data-icon {
    font-size: 50px;
    color: var(--text-secondary);
    opacity: 0.5;
    margin-bottom: 10px;
}

.no-transactions h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.no-transactions p {
    color: var(--text-secondary);
    margin-bottom: 20px;
}

/* Activity Timeline */
.activity-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 25px;
}

.activity-header {
    margin-bottom: 20px;
}

.activity-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.activity-timeline {
    position: relative;
    padding-left: 30px;
}

.activity-timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 10px;
    width: 2px;
    background-color: var(--border-color);
}

.timeline-item {
    position: relative;
    padding-bottom: 20px;
    opacity: 0;
}

.timeline-item:last-child {
    padding-bottom: 0;
}

.timeline-icon {
    position: absolute;
    left: -30px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    z-index: 1;
}

.timeline-icon.income {
    background-color: var(--success-color);
    color: white;
}

.timeline-icon.expense {
    background-color: var(--danger-color);
    color: white;
}

.timeline-content {
    background: var(--surface);
    border-radius: var(--border-radius);
    padding: 15px;
}

.timeline-time {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 5px;
}

.timeline-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 5px;
}

.timeline-amount {
    font-weight: 700;
    font-size: 16px;
}

/* Transactions Table (as fallback) */
.table-container {
    width: 100%;
    overflow-x: auto;
    margin: 30px 0;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    background: var(--primary-color);
    color: var(--text-light);
    font-weight: 600;
    position: sticky;
    top: 0;
}

tr:hover {
    background: var(--surface);
}

/* Filter and Sort Controls */
.controls-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-bottom: 24px;
    padding: 20px;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    position: relative;
    transition: all var(--transition-normal) var(--easing-standard);
    overflow: hidden;
}

.controls-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
    opacity: 0.8;
}

.filter-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-controls label {
    margin-bottom: 0;
    margin-right: 5px;
}

.filter-controls select {
    width: auto;
    padding: 8px 12px;
}

/* Dashboard Layout - Simplified */
.dashboard-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    animation: fadeIn var(--transition-normal) var(--easing-decelerate);
}

@media (min-width: 992px) {
    .dashboard-container {
        grid-template-columns: 3fr 2fr;
    }
}

.dashboard-column {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* Welcome Card - Simplified */
.welcome-card {
    background: var(--gradient-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all var(--transition-normal) var(--easing-standard);
    position: relative;
    overflow: hidden;
}

.welcome-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(rgba(255, 255, 255, 0.1), transparent);
    pointer-events: none;
}

.welcome-card:hover {
    box-shadow: var(--card-shadow-hover);
}

.welcome-content {
    display: flex;
    align-items: center;
    gap: 16px;
    position: relative;
    z-index: 1;
}

.welcome-icon {
    font-size: 24px;
    color: var(--text-light);
    background: rgba(255, 255, 255, 0.2);
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius-circle);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.welcome-text h2 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 4px;
}

.welcome-text p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin-bottom: 0;
}

.date-display {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.15);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    position: relative;
    z-index: 1;
}

.date-display i {
    color: var(--text-light);
}

/* Stats Container - Simplified */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
    width: 100%;
}

.stat-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all var(--transition-fast) var(--easing-standard);
    position: relative;
    overflow: hidden;
}

.income-stat {
    background: var(--gradient-bg-success);
}

.expense-stat {
    background: var(--gradient-bg-danger);
}

.balance-stat {
    background: var(--gradient-bg-primary);
}

.stat-card:hover {
    box-shadow: var(--card-shadow-hover);
}

.stat-icon {
    width: 45px;
    height: 45px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    position: relative;
}

.income-stat .stat-icon {
    background: var(--gradient-success);
    color: white;
}

.expense-stat .stat-icon {
    background: var(--gradient-danger);
    color: white;
}

.balance-stat .stat-icon {
    background: var(--gradient-primary);
    color: white;
}

.stat-info {
    flex: 1;
}

.stat-title {
    color: var(--text-secondary);
    font-size: 14px;
    margin-bottom: 4px;
    font-weight: 500;
}

.stat-value {
    font-size: 22px;
    font-weight: 700;
    color: var(--text-primary);
}

.stat-value.positive {
    color: var(--success-color);
}

.stat-value.negative {
    color: var(--danger-color);
}

/* Charts - Simplified */
.charts-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
}

.chart-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 16px;
    height: 100%;
    transition: all var(--transition-fast) var(--easing-standard);
    position: relative;
    overflow: hidden;
}

.chart-card:hover {
    box-shadow: var(--card-shadow-hover);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
}

.chart-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.chart-title i {
    color: var(--primary-color);
    font-size: 16px;
}

.chart-actions {
    display: flex;
    gap: 8px;
}

.chart-action-btn {
    background: rgba(67, 97, 238, 0.08);
    border: none;
    color: var(--primary-color);
    font-size: 14px;
    cursor: pointer;
    transition: all var(--transition-fast) var(--easing-standard);
    width: 30px;
    height: 30px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-action-btn:hover {
    background: var(--primary-color);
    color: var(--text-light);
}

.chart-container {
    position: relative;
    height: 250px;
    width: 100%;
}

/* Fixed Action Buttons */
.add-transaction-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    border: none;
    z-index: 100;
    transition: all 0.3s ease;
}

.add-transaction-btn:hover {
    transform: translateY(-5px) rotate(90deg);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

/* Modal Styles - Enhanced */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal) var(--easing-decelerate);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--card-shadow-hover);
    width: 90%;
    max-width: 550px;
    max-height: 90vh;
    overflow-y: auto;
    transform: translateY(30px) scale(0.95);
    transition: transform var(--transition-normal) var(--easing-decelerate),
                opacity var(--transition-normal) var(--easing-decelerate);
    opacity: 0;
    position: relative;
}

.modal-overlay.active .modal-container {
    transform: translateY(0) scale(1);
    opacity: 1;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.modal-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: var(--gradient-primary);
    opacity: 0.3;
}

.modal-header h2 {
    font-size: 22px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.modal-header h2 i {
    color: var(--primary-color);
    font-size: 24px;
}

.modal-close {
    background: rgba(239, 71, 111, 0.1);
    border: none;
    color: var(--danger-color);
    font-size: 18px;
    cursor: pointer;
    transition: all var(--transition-fast) var(--easing-standard);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    position: relative;
    overflow: hidden;
}

.modal-close::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(rgba(255, 255, 255, 0.1), transparent);
    border-radius: inherit;
}

.modal-close i {
    position: relative;
    z-index: 1;
    transition: transform var(--transition-fast) var(--easing-standard);
}

.modal-close:hover {
    background: var(--danger-color);
    color: var(--text-light);
    transform: rotate(90deg);
}

.modal-body {
    padding: 30px;
}

/* Table Modal - Enhanced */
.table-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal) var(--easing-decelerate);
}

.table-modal.active {
    opacity: 1;
    visibility: visible;
}

.table-modal-container {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--card-shadow-hover);
    width: 95%;
    max-width: 1000px;
    max-height: 90vh;
    overflow-y: auto;
    transform: translateY(30px) scale(0.95);
    transition: transform var(--transition-normal) var(--easing-decelerate),
                opacity var(--transition-normal) var(--easing-decelerate);
    opacity: 0;
    position: relative;
}

.table-modal.active .table-modal-container {
    transform: translateY(0) scale(1);
    opacity: 1;
}

.table-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    background: var(--card-bg);
    z-index: 10;
}

.table-modal-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: var(--gradient-primary);
    opacity: 0.3;
}

.table-modal-header h2 {
    font-size: 22px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.table-modal-header h2 i {
    color: var(--primary-color);
    font-size: 24px;
}

.table-modal-body {
    padding: 30px;
}

.table-container {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--subtle-shadow);
}

table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

thead {
    background: var(--primary-color);
    color: var(--text-light);
}

th {
    padding: 15px 20px;
    text-align: left;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 5;
}

th:first-child {
    border-top-left-radius: var(--border-radius);
}

th:last-child {
    border-top-right-radius: var(--border-radius);
}

td {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    transition: all var(--transition-fast) var(--easing-standard);
}

tr:last-child td {
    border-bottom: none;
}

tr:last-child td:first-child {
    border-bottom-left-radius: var(--border-radius);
}

tr:last-child td:last-child {
    border-bottom-right-radius: var(--border-radius);
}

tbody tr {
    background: var(--surface);
    transition: all var(--transition-fast) var(--easing-standard);
}

tbody tr:hover {
    background: var(--card-bg);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    display: inline-block;
}

.badge.income {
    background: rgba(6, 214, 160, 0.1);
    color: var(--success-color);
}

.badge.expense {
    background: rgba(239, 71, 111, 0.1);
    color: var(--danger-color);
}

.table-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.table-action-btn {
    width: 32px;
    height: 32px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    background: var(--card-bg);
    transition: all var(--transition-fast) var(--easing-standard);
    text-decoration: none;
    box-shadow: var(--subtle-shadow);
}

.table-action-btn:hover {
    transform: translateY(-2px);
}

.table-action-btn:nth-child(1):hover {
    background: var(--primary-color);
    color: var(--text-light);
}

.table-action-btn:nth-child(2):hover {
    background: var(--danger-color);
    color: var(--text-light);
}

/* Toast Messages */
.toast-message {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%) translateY(100px);
    background-color: var(--card-bg);
    color: var(--text-primary);
    padding: 15px 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 1100;
    opacity: 0;
    transition: all 0.3s ease;
}

.toast-message.show {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
}

.toast-message.success {
    border-left: 4px solid var(--success-color);
}

.toast-message.success i {
    color: var(--success-color);
}

.toast-message.error {
    border-left: 4px solid var(--danger-color);
}

.toast-message.error i {
    color: var(--danger-color);
}

/* Flash Container */
.flash-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    width: 300px;
}

/* Transaction Type Selector */
.transaction-type-selector {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.type-option {
    flex: 1;
}

.type-option input[type="radio"] {
    display: none;
}

.type-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15px;
    border-radius: var(--border-radius);
    border: 2px solid var(--border-color);
    cursor: pointer;
    transition: all 0.2s ease;
    gap: 10px;
}

.type-label i {
    font-size: 20px;
}

.type-label.income {
    color: var(--text-secondary);
}

.type-label.expense {
    color: var(--text-secondary);
}

input[type="radio"]:checked + .type-label.income {
    background-color: rgba(76, 201, 240, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
}

input[type="radio"]:checked + .type-label.expense {
    background-color: rgba(247, 37, 133, 0.1);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

/* Input with Icon */
.input-icon-wrapper {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.input-icon-wrapper input {
    padding-left: 40px;
}

/* Badge */
.badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.badge.income {
    background-color: rgba(76, 201, 240, 0.1);
    color: var(--success-color);
}

.badge.expense {
    background-color: rgba(247, 37, 133, 0.1);
    color: var(--danger-color);
}

/* Brand Icon */
.brand-icon {
    margin-right: 5px;
}

/* Button Block */
.btn-block {
    display: block;
    width: 100%;
}

/* Advanced Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
    from { transform: translateY(-30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideLeft {
    from { transform: translateX(30px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideRight {
    from { transform: translateX(-30px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes zoomIn {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-20px); }
    60% { transform: translateY(-10px); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes shimmer {
    0% { background-position: -1000px 0; }
    100% { background-position: 1000px 0; }
}

.fade-in {
    animation: fadeIn var(--transition-normal) var(--easing-decelerate) forwards;
}

.slide-up {
    animation: slideUp var(--transition-normal) var(--easing-decelerate) forwards;
}

.slide-down {
    animation: slideDown var(--transition-normal) var(--easing-decelerate) forwards;
}

.slide-left {
    animation: slideLeft var(--transition-normal) var(--easing-decelerate) forwards;
}

.slide-right {
    animation: slideRight var(--transition-normal) var(--easing-decelerate) forwards;
}

.zoom-in {
    animation: zoomIn var(--transition-normal) var(--easing-decelerate) forwards;
}

.bounce {
    animation: bounce 1s var(--easing-standard);
}

.pulse {
    animation: pulse 1.5s var(--easing-standard) infinite;
}

/* Shimmer loading effect */
.shimmer {
    background: linear-gradient(90deg,
        var(--card-bg) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        var(--card-bg) 100%);
    background-size: 1000px 100%;
    animation: shimmer 2s infinite linear;
}

/* Staggered animations for lists */
.stagger-animation > * {
    opacity: 0;
}

.stagger-animation > *:nth-child(1) { animation: slideUp 0.4s var(--easing-decelerate) 0.1s forwards; }
.stagger-animation > *:nth-child(2) { animation: slideUp 0.4s var(--easing-decelerate) 0.2s forwards; }
.stagger-animation > *:nth-child(3) { animation: slideUp 0.4s var(--easing-decelerate) 0.3s forwards; }
.stagger-animation > *:nth-child(4) { animation: slideUp 0.4s var(--easing-decelerate) 0.4s forwards; }
.stagger-animation > *:nth-child(5) { animation: slideUp 0.4s var(--easing-decelerate) 0.5s forwards; }
.stagger-animation > *:nth-child(6) { animation: slideUp 0.4s var(--easing-decelerate) 0.6s forwards; }
.stagger-animation > *:nth-child(7) { animation: slideUp 0.4s var(--easing-decelerate) 0.7s forwards; }
.stagger-animation > *:nth-child(8) { animation: slideUp 0.4s var(--easing-decelerate) 0.8s forwards; }
.stagger-animation > *:nth-child(9) { animation: slideUp 0.4s var(--easing-decelerate) 0.9s forwards; }
.stagger-animation > *:nth-child(10) { animation: slideUp 0.4s var(--easing-decelerate) 1.0s forwards; }

/* Hover animations */
.hover-float {
    transition: transform var(--transition-normal) var(--easing-standard);
}

.hover-float:hover {
    transform: translateY(-8px);
}

.hover-scale {
    transition: transform var(--transition-normal) var(--easing-standard);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform var(--transition-normal) var(--easing-standard);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

/* Authentication Pages */
.auth-container {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 40px;
    width: 100%;
    max-width: 500px;
    text-align: center;
    margin: 50px auto;
}

.auth-title {
    font-size: 32px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.auth-subtitle {
    color: var(--text-secondary);
    margin-bottom: 30px;
}

.auth-form {
    text-align: left;
    margin: 0 auto;
}

.auth-footer {
    margin-top: 30px;
    color: var(--text-secondary);
}

.auth-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.auth-footer a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Profile Page Styles */
.profile-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 30px;
    width: 100%;
    max-width: 600px;
    margin: 0 auto 30px;
}

.profile-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
}

.profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: var(--primary-light);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
    color: var(--text-light);
    font-size: 50px;
}

.profile-name {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
}

/* Landing Page Styles */
.hero-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    max-width: 1200px;
    margin: 50px auto;
    padding: 0 20px;
}

.hero-content {
    flex: 1;
    text-align: left;
    padding-right: 40px;
}

.hero-title {
    font-size: 48px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 20px;
    color: var(--text-secondary);
    margin-bottom: 40px;
    line-height: 1.5;
}

.hero-buttons {
    display: flex;
    gap: 20px;
}

.hero-image {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    max-height: 400px;
}

.features-container {
    width: 100%;
    max-width: 1200px;
    margin: 80px auto;
    padding: 0 20px;
    text-align: center;
}

.section-title {
    font-size: 36px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 50px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.feature-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 30px;
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    font-size: 40px;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.feature-title {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-primary);
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

.landing-footer {
    width: 100%;
    text-align: center;
    padding: 30px 0;
    margin-top: 50px;
    color: var(--text-secondary);
    border-top: 1px solid var(--border-color);
}

/* Category Suggestions */
.category-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: var(--card-bg);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    box-shadow: var(--card-shadow);
    z-index: 10;
    max-height: 200px;
    overflow-y: auto;
    display: none;
    border: 1px solid var(--border-color);
    border-top: none;
}

.category-suggestions.active {
    display: block;
    animation: slideDown var(--transition-fast) var(--easing-decelerate);
}

.suggestion-item {
    padding: 12px 16px;
    cursor: pointer;
    transition: all var(--transition-fast) var(--easing-standard);
    display: flex;
    align-items: center;
    gap: 10px;
    border-bottom: 1px solid var(--border-color);
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item i {
    color: var(--primary-color);
    font-size: 14px;
}

.suggestion-item:hover {
    background-color: rgba(67, 97, 238, 0.08);
    color: var(--primary-color);
    padding-left: 20px;
}

/* Quick Filter Bar for Table Modal */
.quick-filter-bar {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
    flex-wrap: wrap;
}

.quick-filter-label {
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.quick-filter-label i {
    color: var(--primary-color);
}

.quick-filter-buttons {
    display: flex;
    gap: 10px;
}

.quick-filter-btn {
    background: var(--surface);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 8px 16px;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast) var(--easing-standard);
}

.quick-filter-btn:hover {
    background: rgba(67, 97, 238, 0.08);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.quick-filter-btn.active {
    background: var(--primary-color);
    color: var(--text-light);
    border-color: var(--primary-color);
}

.quick-search {
    margin-left: auto;
    width: 250px;
}

.quick-search .input-icon-wrapper {
    margin: 0;
}

/* Table Pagination */
.table-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid var(--border-color);
    flex-wrap: wrap;
    gap: 16px;
}

.pagination-info {
    color: var(--text-secondary);
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

.pagination-btn {
    background: rgba(67, 97, 238, 0.08);
    border: none;
    color: var(--primary-color);
    padding: 8px 16px;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast) var(--easing-standard);
    display: flex;
    align-items: center;
    gap: 8px;
}

.pagination-btn:hover:not(:disabled) {
    background: var(--primary-color);
    color: var(--text-light);
    transform: translateY(-2px);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-pages {
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-number {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-fast) var(--easing-standard);
    font-weight: 500;
}

.page-number:hover {
    background: rgba(67, 97, 238, 0.08);
    color: var(--primary-color);
}

.current-page {
    background: var(--primary-color);
    color: var(--text-light);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    font-weight: 600;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    /* Sidebar Responsive */
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .mobile-header {
        display: flex;
    }

    body {
        padding-left: 0;
        padding-top: 60px;
    }

    /* Overlay when sidebar is open */
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .sidebar-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    /* Landing page responsive */
    .hero-container {
        flex-direction: column;
        text-align: center;
        margin: 30px auto;
    }

    .hero-content {
        padding-right: 0;
        margin-bottom: 40px;
    }

    .hero-buttons {
        justify-content: center;
    }

    .hero-title {
        font-size: 36px;
    }

    .hero-subtitle {
        font-size: 18px;
    }

    .section-title {
        font-size: 30px;
    }
}

@media (max-width: 768px) {
    /* Smaller screens adjustments */
    .sidebar {
        width: 280px;
    }

    .dashboard-container {
        grid-template-columns: 1fr;
    }

    .stats-container {
        grid-template-columns: 1fr;
    }

    .theme-toggle {
        display: none; /* Hide desktop theme toggle on mobile */
    }

    .controls-container {
        flex-direction: column;
        gap: 15px;
    }

    .filter-controls {
        width: 100%;
        justify-content: space-between;
    }

    form {
        padding: 20px;
    }

    .auth-container {
        padding: 30px 20px;
        margin: 30px auto;
    }

    .hero-title {
        font-size: 28px;
    }

    .hero-subtitle {
        font-size: 16px;
        margin-bottom: 30px;
    }

    .feature-card {
        padding: 20px;
    }
}
