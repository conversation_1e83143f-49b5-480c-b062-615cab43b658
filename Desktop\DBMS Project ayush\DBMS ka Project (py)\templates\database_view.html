<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Explorer - Finance Tracker</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link href="https://fonts.cdnfonts.com/css/achiko" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <style>
        .database-explorer {
            padding: 20px;
        }
        .back-link {
            margin-bottom: 20px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <!-- Theme Toggle Button -->
    <button class="theme-toggle" id="theme-toggle" title="Toggle dark/light mode">
        <i class="fas fa-moon"></i>
    </button>

    <div class="database-explorer">
<a href="{{ url_for('dashboard') }}" class="back-link btn btn-outline-primary">
    <i class="fas fa-arrow-left"></i> Back to Dashboard
</a>

<div class="container mt-5">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Database Explorer</h3>
                </div>
                <div class="card-body">
                    <p class="lead">This page shows the contents of your SQLite database.</p>
                </div>
            </div>

            {% for table in tables %}
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h4 class="mb-0">Table: {{ table.name }}</h4>
                </div>
                <div class="card-body">
                    <h5>Schema</h5>
                    <div class="table-responsive mb-4">
                        <table class="table table-sm table-bordered">
                            <thead class="thead-light">
                                <tr>
                                    <th>Column ID</th>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Not Null</th>
                                    <th>Default</th>
                                    <th>Primary Key</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for column in table.schema %}
                                <tr>
                                    <td>{{ column[0] }}</td>
                                    <td>{{ column[1] }}</td>
                                    <td>{{ column[2] }}</td>
                                    <td>{{ column[3] }}</td>
                                    <td>{{ column[4] }}</td>
                                    <td>{{ column[5] }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <h5>Data ({{ table.data|length }} rows)</h5>
                    {% if table.data %}
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered">
                            <thead class="thead-dark">
                                <tr>
                                    {% for column in table.schema %}
                                    <th>{{ column[1] }}</th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for row in table.data %}
                                <tr>
                                    {% for cell in row %}
                                    <td>{{ cell }}</td>
                                    {% endfor %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">No data in this table.</div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
    </div>

    <script>
        // Theme Toggle Functionality
        const themeToggle = document.getElementById('theme-toggle');
        const themeIcon = themeToggle.querySelector('i');

        // Check for saved theme preference or default to light
        const currentTheme = localStorage.getItem('theme') || 'light';
        if (currentTheme === 'dark') {
            document.body.classList.add('dark-theme');
            themeIcon.classList.replace('fa-moon', 'fa-sun');
        }

        // Toggle theme function
        function toggleTheme() {
            document.body.classList.toggle('dark-theme');

            // Update icons
            if (document.body.classList.contains('dark-theme')) {
                themeIcon.classList.replace('fa-moon', 'fa-sun');
                localStorage.setItem('theme', 'dark');
            } else {
                themeIcon.classList.replace('fa-sun', 'fa-moon');
                localStorage.setItem('theme', 'light');
            }
        }

        // Toggle theme when button is clicked
        themeToggle.addEventListener('click', toggleTheme);
    </script>
</body>
</html>
