<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Transaction - Finance Tracker</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
    <!-- Theme Toggle Button -->
    <button class="theme-toggle" id="theme-toggle" title="Toggle dark/light mode">
        <i class="fas fa-moon"></i>
    </button>

    <!-- Sidebar Navigation -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="app-logo">
                <i class="fas fa-chart-line"></i>
            </div>
            <h1 class="app-name">Finance Tracker</h1>
        </div>

        <div class="sidebar-menu">
            <a href="{{ url_for('dashboard') }}" class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-home"></i>
                </div>
                <span class="menu-text">Dashboard</span>
            </a>
            <a href="{{ url_for('profile') }}" class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-user"></i>
                </div>
                <span class="menu-text">Profile</span>
            </a>
            <a href="{{ url_for('logout') }}" class="menu-item logout">
                <div class="menu-icon">
                    <i class="fas fa-sign-out-alt"></i>
                </div>
                <span class="menu-text">Logout</span>
            </a>
        </div>

        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="user-name">User</div>
            </div>
        </div>
    </div>

    <!-- Mobile Header -->
    <div class="mobile-header">
        <button class="menu-toggle" id="menu-toggle">
            <i class="fas fa-bars"></i>
        </button>
        <div class="mobile-logo">Finance Tracker</div>
        <div class="mobile-actions">
            <button class="theme-toggle-mobile" id="theme-toggle-mobile">
                <i class="fas fa-moon"></i>
            </button>
        </div>
    </div>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebar-overlay"></div>

    <div class="main-container">
        <div class="profile-container">
            <h1>Edit Transaction</h1>
            <a href="{{ url_for('dashboard') }}" class="btn btn-outline">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
          {% if messages %}
            <ul class="flashes">
              {% for category, message in messages %}
                <li class="{{ category }}">{{ message }}</li>
              {% endfor %}
            </ul>
          {% endif %}
        {% endwith %}

        <form method="POST" class="slide-up">
            <div class="form-group">
                <label for="amount">Amount</label>
                <input type="number" step="0.01" id="amount" name="amount" placeholder="Enter amount" value="{{ transaction.amount }}" required>
            </div>

            <div class="form-group">
                <label for="category">Category</label>
                <input type="text" id="category" name="category" placeholder="Enter category" value="{{ transaction.category }}" required>
            </div>

            <div class="form-group">
                <label for="transaction_type">Transaction Type</label>
                <select id="transaction_type" name="transaction_type" required>
                    <option value="income" {% if transaction.transaction_type=='income' %}selected{% endif %}>Income</option>
                    <option value="expense" {% if transaction.transaction_type=='expense' %}selected{% endif %}>Expense</option>
                </select>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn-primary">
                    <i class="fas fa-save"></i> Update Transaction
                </button>
                <a href="{{ url_for('dashboard') }}" class="btn btn-outline">Cancel</a>
            </div>
        </form>
    </div>

    <script>
        // Theme Toggle Functionality
        const themeToggle = document.getElementById('theme-toggle');
        const themeToggleMobile = document.getElementById('theme-toggle-mobile');
        const themeIcon = themeToggle.querySelector('i');
        const themeIconMobile = themeToggleMobile.querySelector('i');

        // Check for saved theme preference or default to light
        const currentTheme = localStorage.getItem('theme') || 'light';
        if (currentTheme === 'dark') {
            document.body.classList.add('dark-theme');
            themeIcon.classList.replace('fa-moon', 'fa-sun');
            themeIconMobile.classList.replace('fa-moon', 'fa-sun');
        }

        // Toggle theme function
        function toggleTheme() {
            document.body.classList.toggle('dark-theme');

            // Update icons
            if (document.body.classList.contains('dark-theme')) {
                themeIcon.classList.replace('fa-moon', 'fa-sun');
                themeIconMobile.classList.replace('fa-moon', 'fa-sun');
                localStorage.setItem('theme', 'dark');
            } else {
                themeIcon.classList.replace('fa-sun', 'fa-moon');
                themeIconMobile.classList.replace('fa-sun', 'fa-moon');
                localStorage.setItem('theme', 'light');
            }
        }

        // Toggle theme when buttons are clicked
        themeToggle.addEventListener('click', toggleTheme);
        themeToggleMobile.addEventListener('click', toggleTheme);

        // Sidebar Toggle Functionality
        const menuToggle = document.getElementById('menu-toggle');
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebar-overlay');

        // Toggle sidebar when menu button is clicked
        menuToggle.addEventListener('click', () => {
            sidebar.classList.toggle('active');
            sidebarOverlay.classList.toggle('active');
            document.body.style.overflow = sidebar.classList.contains('active') ? 'hidden' : '';
        });

        // Close sidebar when clicking on overlay
        sidebarOverlay.addEventListener('click', () => {
            sidebar.classList.remove('active');
            sidebarOverlay.classList.remove('active');
            document.body.style.overflow = '';
        });
    </script>
</body>
</html>
