<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Finance Tracker</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Theme Toggle Button -->
    <button class="theme-toggle" id="theme-toggle" title="Toggle dark/light mode">
        <i class="fas fa-moon"></i>
    </button>

    <!-- Add Transaction Button (Fixed) -->
    <button class="add-transaction-btn" id="add-transaction-btn" title="Add new transaction">
        <i class="fas fa-plus"></i>
    </button>

    <!-- Sidebar Navigation -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="app-logo">
                <i class="fas fa-chart-line"></i>
            </div>
            <h1 class="app-name">Finance Tracker</h1>
        </div>

        <div class="sidebar-menu">
            <a href="{{ url_for('dashboard') }}" class="menu-item active">
                <div class="menu-icon">
                    <i class="fas fa-home"></i>
                </div>
                <span class="menu-text">Dashboard</span>
            </a>
            <a href="{{ url_for('profile') }}" class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-user"></i>
                </div>
                <span class="menu-text">Profile</span>
            </a>
            <a href="{{ url_for('database_explorer') }}" class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-database"></i>
                </div>
                <span class="menu-text">Database Explorer</span>
            </a>
            <a href="{{ url_for('logout') }}" class="menu-item logout">
                <div class="menu-icon">
                    <i class="fas fa-sign-out-alt"></i>
                </div>
                <span class="menu-text">Logout</span>
            </a>
        </div>

        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="user-name">{{ username }}</div>
            </div>
        </div>
    </div>

    <!-- Mobile Header -->
    <div class="mobile-header">
        <button class="menu-toggle" id="menu-toggle">
            <i class="fas fa-bars"></i>
        </button>
        <div class="mobile-logo">Finance Tracker</div>
        <div class="mobile-actions">
            <button class="theme-toggle-mobile" id="theme-toggle-mobile">
                <i class="fas fa-moon"></i>
            </button>
        </div>
    </div>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        <div class="flash-container">
            <ul class="flashes">
            {% for category, message in messages %}
               <li class="{{ category }}">
                   <i class="fas {% if category == 'success' %}fa-check-circle{% elif category == 'danger' %}fa-exclamation-circle{% else %}fa-info-circle{% endif %}"></i>
                   {{ message }}
                   <button class="close-flash"><i class="fas fa-times"></i></button>
               </li>
            {% endfor %}
            </ul>
        </div>
      {% endif %}
    {% endwith %}

    <div class="dashboard-container">
        <!-- Left Column - Financial Overview -->
        <div class="dashboard-column">
            <!-- Welcome Message -->
            <div class="welcome-card slide-down">
                <div class="welcome-content">
                    <div class="welcome-icon">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="welcome-text">
                        <h2>Welcome back, {{ username }}!</h2>
                        <p>Here's your financial overview</p>
                    </div>
                </div>
                <div class="date-display">
                    <i class="far fa-calendar-alt"></i>
                    <span id="current-date"></span>
                </div>
            </div>

            <!-- Dashboard Stats -->
            <div class="stats-container stagger-animation">
                <div class="stat-card income-stat hover-float">
                    <div class="stat-icon">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-title">Total Income</div>
                        <div class="stat-value" id="total-income">$0.00</div>
                    </div>
                </div>
                <div class="stat-card expense-stat hover-float">
                    <div class="stat-icon">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-title">Total Expenses</div>
                        <div class="stat-value" id="total-expenses">$0.00</div>
                    </div>
                </div>
                <div class="stat-card balance-stat hover-float">
                    <div class="stat-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-title">Balance</div>
                        <div class="stat-value" id="balance">$0.00</div>
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="charts-container">
                <div class="chart-card zoom-in" style="animation-delay: 0.3s;">
                    <div class="chart-header">
                        <div class="chart-title">
                            <i class="fas fa-chart-bar"></i> Income vs Expenses
                        </div>
                        <div class="chart-actions">
                            <button class="chart-action-btn" title="Download chart">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="income-expense-chart"></canvas>
                    </div>
                </div>

                <div class="chart-card zoom-in" style="animation-delay: 0.5s;">
                    <div class="chart-header">
                        <div class="chart-title">
                            <i class="fas fa-chart-pie"></i> Spending by Category
                        </div>
                        <div class="chart-actions">
                            <button class="chart-action-btn" title="Download chart">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="category-chart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column - Transactions -->
        <div class="dashboard-column">
            <!-- Filter and Sort Controls -->
            <div class="controls-container slide-left" style="animation-delay: 0.2s;">
                <div class="controls-header">
                    <h3><i class="fas fa-list"></i> Recent Transactions</h3>
                    <button class="view-all-btn" id="view-all-btn">
                        <span class="btn-icon"><i class="fas fa-table"></i></span>
                        <span class="btn-text">View All</span>
                    </button>
                </div>
                <div class="filter-controls">
                    <div class="filter-title">
                        <i class="fas fa-sliders-h"></i> Filter & Sort
                    </div>
                    <div class="filter-options">
                        <form method="GET" action="{{ url_for('dashboard') }}" class="filter-form">
                            <div class="filter-buttons">
                                <div class="filter-label">Filter:</div>
                                <div class="button-group">
                                    <input type="radio" id="filter-all" name="filter" value="" {% if not request.args.get('filter') %}checked{% endif %} class="filter-radio">
                                    <label for="filter-all" class="filter-btn-option">All</label>

                                    <input type="radio" id="filter-income" name="filter" value="income" {% if request.args.get('filter') == 'income' %}checked{% endif %} class="filter-radio">
                                    <label for="filter-income" class="filter-btn-option income">Income</label>

                                    <input type="radio" id="filter-expense" name="filter" value="expense" {% if request.args.get('filter') == 'expense' %}checked{% endif %} class="filter-radio">
                                    <label for="filter-expense" class="filter-btn-option expense">Expense</label>
                                </div>
                            </div>

                            <div class="sort-buttons">
                                <div class="filter-label">Sort by:</div>
                                <div class="button-group">
                                    <input type="radio" id="sort-date" name="sort" value="date" {% if request.args.get('sort') != 'amount' %}checked{% endif %} class="filter-radio">
                                    <label for="sort-date" class="filter-btn-option">Date</label>

                                    <input type="radio" id="sort-amount" name="sort" value="amount" {% if request.args.get('sort') == 'amount' %}checked{% endif %} class="filter-radio">
                                    <label for="sort-amount" class="filter-btn-option">Amount</label>
                                </div>
                            </div>

                            <button type="submit" class="apply-btn">
                                <i class="fas fa-check"></i> Apply
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Transactions as Cards -->
            <div class="transactions-container stagger-animation">
                {% if transactions %}
                    {% for txn in transactions %}
                    <div class="transaction-card {{ txn.transaction_type }} hover-float">
                        <div class="transaction-icon">
                            <i class="fas {% if txn.transaction_type == 'income' %}fa-arrow-up{% else %}fa-arrow-down{% endif %}"></i>
                        </div>
                        <div class="transaction-details">
                            <div class="transaction-primary">
                                <div class="transaction-category">{{ txn.category }}</div>
                                <div class="transaction-amount">${{ txn.amount }}</div>
                            </div>
                            <div class="transaction-secondary">
                                <div class="transaction-date">
                                    <i class="far fa-calendar"></i> {{ txn.date.strftime('%Y-%m-%d') }}
                                </div>
                                <div class="transaction-time">
                                    <i class="far fa-clock"></i> {{ txn.date.strftime('%H:%M') }}
                                </div>
                            </div>
                        </div>
                        <div class="transaction-actions">
                            <a href="{{ url_for('edit_transaction', transaction_id=txn.id) }}" class="action-btn edit-btn" title="Edit transaction">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{{ url_for('delete_transaction', transaction_id=txn.id) }}" class="action-btn delete-btn" title="Delete transaction" onclick="return confirm('Are you sure you want to delete this transaction?')">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="no-transactions slide-up">
                        <div class="no-data-icon pulse">
                            <i class="fas fa-receipt"></i>
                        </div>
                        <h3>No transactions yet</h3>
                        <p>Add your first transaction to start tracking your finances</p>
                        <button class="btn-primary" id="no-data-add-btn">
                            <i class="fas fa-plus"></i> Add Transaction
                        </button>
                    </div>
                {% endif %}
            </div>

            <!-- Recent Activity Timeline -->
            <div class="activity-card slide-up" style="animation-delay: 0.4s;">
                <div class="activity-header">
                    <h3><i class="fas fa-history"></i> Recent Activity</h3>
                </div>
                <div class="activity-timeline">
                    {% for txn in transactions[:5] %}
                    <div class="timeline-item">
                        <div class="timeline-icon {{ txn.transaction_type }}">
                            <i class="fas {% if txn.transaction_type == 'income' %}fa-arrow-up{% else %}fa-arrow-down{% endif %}"></i>
                        </div>
                        <div class="timeline-content hover-scale">
                            <div class="timeline-time">{{ txn.date.strftime('%H:%M') }}</div>
                            <div class="timeline-title">{{ txn.category }}</div>
                            <div class="timeline-amount">${{ txn.amount }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Add Transaction Modal -->
    <div class="modal-overlay" id="transaction-modal">
        <div class="modal-container">
            <div class="modal-header">
                <h2><i class="fas fa-plus-circle"></i> Add New Transaction</h2>
                <button class="modal-close" id="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form method="POST" id="transaction-form" class="stagger-animation">
                    <div class="form-group">
                        <label for="amount">Amount</label>
                        <div class="input-icon-wrapper">
                            <i class="fas fa-dollar-sign input-icon"></i>
                            <input type="number" step="0.01" name="amount" id="amount" placeholder="Enter amount" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="category">Category</label>
                        <div class="input-icon-wrapper">
                            <i class="fas fa-tag input-icon"></i>
                            <input type="text" name="category" id="category" placeholder="Enter category" required>
                        </div>
                        <div class="category-suggestions" id="category-suggestions">
                            <!-- Suggestions will be populated by JavaScript -->
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="transaction_type">Transaction Type</label>
                        <div class="transaction-type-selector">
                            <div class="type-option">
                                <input type="radio" id="type-income" name="transaction_type" value="income" required>
                                <label for="type-income" class="type-label income">
                                    <i class="fas fa-arrow-up"></i> Income
                                </label>
                            </div>
                            <div class="type-option">
                                <input type="radio" id="type-expense" name="transaction_type" value="expense">
                                <label for="type-expense" class="type-label expense">
                                    <i class="fas fa-arrow-down"></i> Expense
                                </label>
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn-primary btn-block hover-float">
                        <i class="fas fa-plus"></i> Add Transaction
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Transactions Table (hidden by default) -->
    <div class="table-modal" id="table-modal">
        <div class="table-modal-container">
            <div class="table-modal-header">
                <h2><i class="fas fa-table"></i> All Transactions</h2>
                <button class="modal-close" id="table-modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="table-modal-body">
                <!-- Quick Filter Controls -->
                <div class="quick-filter-bar">
                    <div class="quick-filter-label">Quick Filter:</div>
                    <div class="quick-filter-buttons">
                        <button class="quick-filter-btn active" data-filter="all">All</button>
                        <button class="quick-filter-btn" data-filter="income">Income</button>
                        <button class="quick-filter-btn" data-filter="expense">Expenses</button>
                    </div>
                    <div class="quick-search">
                        <div class="input-icon-wrapper">
                            <i class="fas fa-search input-icon"></i>
                            <input type="text" id="table-search" placeholder="Search transactions...">
                        </div>
                    </div>
                </div>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Category</th>
                                <th>Amount</th>
                                <th>Type</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="transactions-table-body">
                            {% for txn in transactions %}
                            <tr data-type="{{ txn.transaction_type }}" data-category="{{ txn.category }}">
                                <td>{{ txn.date.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>{{ txn.category }}</td>
                                <td>${{ txn.amount }}</td>
                                <td>
                                    <span class="badge {{ txn.transaction_type }}">
                                        {{ txn.transaction_type }}
                                    </span>
                                </td>
                                <td>
                                    <div class="table-actions">
                                        <a href="{{ url_for('edit_transaction', transaction_id=txn.id) }}" class="table-action-btn" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('delete_transaction', transaction_id=txn.id) }}" class="table-action-btn" title="Delete" onclick="return confirm('Are you sure you want to delete this transaction?')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Table Pagination -->
                <div class="table-pagination">
                    <div class="pagination-info">Showing <span id="showing-count">{{ transactions|length }}</span> of <span id="total-count">{{ transactions|length }}</span> transactions</div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" id="prev-page" disabled>
                            <i class="fas fa-chevron-left"></i> Previous
                        </button>
                        <div class="pagination-pages" id="pagination-pages">
                            <span class="current-page">1</span>
                        </div>
                        <button class="pagination-btn" id="next-page" disabled>
                            Next <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebar-overlay"></div>

    <script>
        // Theme Toggle Functionality
        const themeToggle = document.getElementById('theme-toggle');
        const themeToggleMobile = document.getElementById('theme-toggle-mobile');
        const themeIcon = themeToggle.querySelector('i');
        const themeIconMobile = themeToggleMobile.querySelector('i');

        // Check for saved theme preference or default to light
        const currentTheme = localStorage.getItem('theme') || 'light';
        if (currentTheme === 'dark') {
            document.body.classList.add('dark-theme');
            themeIcon.classList.replace('fa-moon', 'fa-sun');
            themeIconMobile.classList.replace('fa-moon', 'fa-sun');
        }

        // Toggle theme function
        function toggleTheme() {
            document.body.classList.toggle('dark-theme');

            // Update icons
            if (document.body.classList.contains('dark-theme')) {
                themeIcon.classList.replace('fa-moon', 'fa-sun');
                themeIconMobile.classList.replace('fa-moon', 'fa-sun');
                localStorage.setItem('theme', 'dark');
            } else {
                themeIcon.classList.replace('fa-sun', 'fa-moon');
                themeIconMobile.classList.replace('fa-sun', 'fa-moon');
                localStorage.setItem('theme', 'light');
            }
        }

        // Toggle theme when buttons are clicked
        themeToggle.addEventListener('click', toggleTheme);
        themeToggleMobile.addEventListener('click', toggleTheme);

        // Sidebar Toggle Functionality
        const menuToggle = document.getElementById('menu-toggle');
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebar-overlay');

        // Toggle sidebar when menu button is clicked
        menuToggle.addEventListener('click', () => {
            sidebar.classList.toggle('active');
            sidebarOverlay.classList.toggle('active');
            document.body.style.overflow = sidebar.classList.contains('active') ? 'hidden' : '';
        });

        // Close sidebar when clicking on overlay
        sidebarOverlay.addEventListener('click', () => {
            sidebar.classList.remove('active');
            sidebarOverlay.classList.remove('active');
            document.body.style.overflow = '';
        });

        // Modal Functionality
        const transactionModal = document.getElementById('transaction-modal');
        const addTransactionBtn = document.getElementById('add-transaction-btn');
        const modalClose = document.getElementById('modal-close');
        const tableModal = document.getElementById('table-modal');
        const viewAllBtn = document.getElementById('view-all-btn');
        const tableModalClose = document.getElementById('table-modal-close');
        const noDataAddBtn = document.getElementById('no-data-add-btn');

        // Open transaction modal
        if (addTransactionBtn) {
            addTransactionBtn.addEventListener('click', () => {
                transactionModal.classList.add('active');
                document.body.style.overflow = 'hidden'; // Prevent scrolling
            });
        }

        // Close transaction modal
        if (modalClose) {
            modalClose.addEventListener('click', () => {
                transactionModal.classList.remove('active');
                document.body.style.overflow = ''; // Re-enable scrolling
            });
        }

        // Open table modal
        if (viewAllBtn) {
            viewAllBtn.addEventListener('click', () => {
                tableModal.classList.add('active');
                document.body.style.overflow = 'hidden'; // Prevent scrolling
            });
        }

        // Close table modal
        if (tableModalClose) {
            tableModalClose.addEventListener('click', () => {
                tableModal.classList.remove('active');
                document.body.style.overflow = ''; // Re-enable scrolling
            });
        }

        // No data add button
        if (noDataAddBtn) {
            noDataAddBtn.addEventListener('click', () => {
                transactionModal.classList.add('active');
                document.body.style.overflow = 'hidden'; // Prevent scrolling
            });
        }

        // Close modals when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target === transactionModal) {
                transactionModal.classList.remove('active');
                document.body.style.overflow = '';
            }
            if (e.target === tableModal) {
                tableModal.classList.remove('active');
                document.body.style.overflow = '';
            }
        });

        // Close flash messages
        const closeFlashBtns = document.querySelectorAll('.close-flash');
        closeFlashBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const flashItem = btn.closest('li');
                flashItem.style.opacity = '0';
                setTimeout(() => {
                    flashItem.style.display = 'none';
                }, 300);
            });
        });

        // Display current date
        const currentDateElement = document.getElementById('current-date');
        if (currentDateElement) {
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            currentDateElement.textContent = now.toLocaleDateString('en-US', options);
        }

        // Form submission with AJAX
        const transactionForm = document.getElementById('transaction-form');
        if (transactionForm) {
            transactionForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                const formData = new FormData(this);
                const data = Object.fromEntries(formData.entries());

                try {
                    const response = await fetch("{{ url_for('dashboard') }}", {
                        method: "POST",
                        body: new URLSearchParams(data)
                    });

                    if(response.ok) {
                        // Show success message and reload
                        const successMessage = document.createElement('div');
                        successMessage.className = 'toast-message success';
                        successMessage.innerHTML = '<i class="fas fa-check-circle"></i> Transaction added successfully!';
                        document.body.appendChild(successMessage);

                        setTimeout(() => {
                            successMessage.classList.add('show');
                        }, 100);

                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        // Show error message
                        const errorMessage = document.createElement('div');
                        errorMessage.className = 'toast-message error';
                        errorMessage.innerHTML = '<i class="fas fa-exclamation-circle"></i> Failed to add transaction.';
                        document.body.appendChild(errorMessage);

                        setTimeout(() => {
                            errorMessage.classList.add('show');
                        }, 100);

                        setTimeout(() => {
                            errorMessage.classList.remove('show');
                            setTimeout(() => {
                                document.body.removeChild(errorMessage);
                            }, 300);
                        }, 3000);
                    }
                } catch(err) {
                    console.error(err);
                }
            });
        }

        // Chart download functionality
        const chartActionBtns = document.querySelectorAll('.chart-action-btn');
        chartActionBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const chartCard = this.closest('.chart-card');
                const canvas = chartCard.querySelector('canvas');
                const chartTitle = chartCard.querySelector('.chart-title').textContent.trim();

                // Create a temporary link to download the image
                const link = document.createElement('a');
                link.download = `${chartTitle.replace(/\s+/g, '-').toLowerCase()}.png`;
                link.href = canvas.toDataURL('image/png');
                link.click();
            });
        });

        // Calculate and display statistics
        function calculateStats() {
            let totalIncome = 0;
            let totalExpenses = 0;

            // Get all transactions from the page
            const transactions = [];
            {% for txn in transactions %}
            transactions.push({
                amount: {{ txn.amount }},
                type: "{{ txn.transaction_type }}",
                category: "{{ txn.category }}",
                date: "{{ txn.date.strftime('%Y-%m-%d') }}"
            });
            {% endfor %}

            // Calculate totals
            transactions.forEach(txn => {
                if (txn.type === 'income') {
                    totalIncome += txn.amount;
                } else if (txn.type === 'expense') {
                    totalExpenses += txn.amount;
                }
            });

            const balance = totalIncome - totalExpenses;

            // Update the stats display
            const incomeElement = document.getElementById('total-income');
            const expensesElement = document.getElementById('total-expenses');
            const balanceElement = document.getElementById('balance');

            if (incomeElement) incomeElement.textContent = '$' + totalIncome.toFixed(2);
            if (expensesElement) expensesElement.textContent = '$' + totalExpenses.toFixed(2);
            if (balanceElement) {
                balanceElement.textContent = '$' + balance.toFixed(2);
                // Add color based on balance
                if (balance > 0) {
                    balanceElement.classList.add('positive');
                } else if (balance < 0) {
                    balanceElement.classList.add('negative');
                }
            }

            return { transactions, totalIncome, totalExpenses, balance };
        }

        // Initialize charts
        function initCharts() {
            const { transactions, totalIncome, totalExpenses } = calculateStats();

            // Only initialize charts if elements exist
            const incomeExpenseCtx = document.getElementById('income-expense-chart');
            const categoryCtx = document.getElementById('category-chart');

            if (incomeExpenseCtx) {
                // Income vs Expenses Chart
                new Chart(incomeExpenseCtx.getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: ['Income', 'Expenses'],
                        datasets: [{
                            label: 'Amount ($)',
                            data: [totalIncome, totalExpenses],
                            backgroundColor: [
                                'rgba(76, 201, 240, 0.6)',
                                'rgba(247, 37, 133, 0.6)'
                            ],
                            borderColor: [
                                'rgba(76, 201, 240, 1)',
                                'rgba(247, 37, 133, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(200, 200, 200, 0.1)'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                                padding: 10,
                                cornerRadius: 6,
                                titleFont: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            }
                        }
                    }
                });
            }

            if (categoryCtx && transactions.length > 0) {
                // Category Chart
                const categoryData = {};
                transactions.forEach(txn => {
                    if (txn.type === 'expense') {
                        if (!categoryData[txn.category]) {
                            categoryData[txn.category] = 0;
                        }
                        categoryData[txn.category] += txn.amount;
                    }
                });

                const categories = Object.keys(categoryData);
                const categoryAmounts = Object.values(categoryData);

                // Generate colors for each category
                const categoryColors = categories.map((_, index) => {
                    const hue = (index * 137) % 360; // Golden angle approximation for good distribution
                    return `hsl(${hue}, 70%, 60%)`;
                });

                new Chart(categoryCtx.getContext('2d'), {
                    type: 'doughnut',
                    data: {
                        labels: categories,
                        datasets: [{
                            data: categoryAmounts,
                            backgroundColor: categoryColors,
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        cutout: '70%',
                        plugins: {
                            legend: {
                                position: 'right',
                                labels: {
                                    boxWidth: 15,
                                    padding: 15,
                                    font: {
                                        size: 12
                                    }
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                                padding: 10,
                                cornerRadius: 6,
                                callbacks: {
                                    label: function(context) {
                                        const value = context.raw;
                                        const percentage = ((value / totalExpenses) * 100).toFixed(1);
                                        return `$${value.toFixed(2)} (${percentage}%)`;
                                    }
                                }
                            }
                        }
                    }
                });
            }
        }

        // Initialize everything when the page loads
        window.addEventListener('load', () => {
            calculateStats();
            initCharts();

            // Add animation delay to transaction cards
            const cards = document.querySelectorAll('.transaction-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.05}s`;
            });

            // Add animation delay to timeline items
            const timelineItems = document.querySelectorAll('.timeline-item');
            timelineItems.forEach((item, index) => {
                item.style.animationDelay = `${index * 0.1}s`;
                item.classList.add('slide-up');
            });
        });
    </script>
</body>
</html>
