from flask import Flask, render_template, request, redirect, url_for, session, flash
from flask_sqlalchemy import SQLAlchemy
from flask_bcrypt import Bcrypt
from datetime import datetime
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key_here'  # Replace with a strong secret key!
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///database.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
bcrypt = Bcrypt(app)

# ------------------------
# Database Models
# ------------------------
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(150), unique=True, nullable=False)
    password = db.Column(db.String(150), nullable=False)

class Transaction(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    category = db.Column(db.String(100), nullable=False)
    transaction_type = db.Column(db.String(50), nullable=False)  # income or expense
    date = db.Column(db.DateTime, default=datetime.utcnow)

with app.app_context():
    db.create_all()

# ------------------------
# Routes
# ------------------------

# Landing page
@app.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return render_template('index.html')

# Registration route (Signup)
@app.route('/register', methods=['GET', 'POST'])
def register():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        confirm_password = request.form.get('confirm_password', '')

        # Validate input
        if not username or not password:
            flash("Both username and password are required.", "danger")
            return render_template('register.html')

        if password != confirm_password:
            flash("Passwords do not match.", "danger")
            return render_template('register.html')

        if User.query.filter_by(username=username).first():
            flash("User already exists! Please login.", "danger")
            return render_template('register.html')

        # Create new user
        hashed_password = bcrypt.generate_password_hash(password).decode('utf-8')
        new_user = User(username=username, password=hashed_password)
        db.session.add(new_user)
        db.session.commit()

        flash("Registration successful. Please login.", "success")
        return redirect(url_for('login'))
    return render_template('register.html')

# Login route (Signin)
@app.route('/login', methods=['GET', 'POST'])
def login():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        user = User.query.filter_by(username=username).first()
        if user and bcrypt.check_password_hash(user.password, password):
            session['user_id'] = user.id
            session['username'] = user.username
            flash("Login successful!", "success")
            return redirect(url_for('dashboard'))
        else:
            flash("Invalid username or password.", "danger")
    return render_template('login.html')

# Dashboard route with transaction management
@app.route('/dashboard', methods=['GET', 'POST'])
def dashboard():
    if 'user_id' not in session:
        flash("Please log in to access the dashboard.", "warning")
        return redirect(url_for('login'))

    # Handle new transaction submission as before…
    if request.method == 'POST':
        # (Same transaction creation code as before)
        try:
            amount = float(request.form.get('amount', 0))
        except ValueError:
            flash("Please enter a valid number for amount.", "danger")
            return redirect(url_for('dashboard'))
        category = request.form.get('category', '').strip()
        transaction_type = request.form.get('transaction_type', '').strip()
        if not amount or not category or transaction_type not in ['income', 'expense']:
            flash("Please fill out all fields correctly.", "danger")
            return redirect(url_for('dashboard'))
        new_transaction = Transaction(
            user_id=session['user_id'],
            amount=amount,
            category=category,
            transaction_type=transaction_type
        )
        db.session.add(new_transaction)
        db.session.commit()
        flash("Transaction added successfully.", "success")
        return redirect(url_for('dashboard'))

    # Filtering and Sorting
    filter_type = request.args.get('filter')  # e.g., 'income' or 'expense'
    sort_by = request.args.get('sort')  # e.g., 'amount', 'date'

    query = Transaction.query.filter_by(user_id=session['user_id'])
    if filter_type in ['income', 'expense']:
        query = query.filter_by(transaction_type=filter_type)
    if sort_by == 'amount':
        query = query.order_by(Transaction.amount.desc())
    else:
        # default sort: latest first
        query = query.order_by(Transaction.date.desc())

    transactions = query.all()
    return render_template('dashboard.html', username=session.get('username'), transactions=transactions)


# Delete a transaction
@app.route('/delete_transaction/<int:transaction_id>')
def delete_transaction(transaction_id):
    if 'user_id' not in session:
        flash("Please log in.", "warning")
        return redirect(url_for('login'))
    transaction = Transaction.query.get_or_404(transaction_id)
    if transaction.user_id != session['user_id']:
        flash("Unauthorized action.", "danger")
        return redirect(url_for('dashboard'))
    db.session.delete(transaction)
    db.session.commit()
    flash("Transaction deleted successfully.", "success")
    return redirect(url_for('dashboard'))

# Edit Transaction Route (GET to show form, POST to update)
@app.route('/edit_transaction/<int:transaction_id>', methods=['GET', 'POST'])
def edit_transaction(transaction_id):
    if 'user_id' not in session:
        flash("Please log in.", "warning")
        return redirect(url_for('login'))
    transaction = Transaction.query.get_or_404(transaction_id)
    if transaction.user_id != session['user_id']:
        flash("Unauthorized action.", "danger")
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        try:
            amount = float(request.form.get('amount', 0))
        except ValueError:
            flash("Invalid amount.", "danger")
            return redirect(url_for('edit_transaction', transaction_id=transaction_id))
        category = request.form.get('category', '').strip()
        transaction_type = request.form.get('transaction_type', '').strip()
        if not amount or not category or transaction_type not in ['income', 'expense']:
            flash("Fill out all fields correctly.", "danger")
            return redirect(url_for('edit_transaction', transaction_id=transaction_id))

        transaction.amount = amount
        transaction.category = category
        transaction.transaction_type = transaction_type
        db.session.commit()
        flash("Transaction updated successfully.", "success")
        return redirect(url_for('dashboard'))

    return render_template('edit_transaction.html', transaction=transaction)

@app.route('/profile', methods=['GET', 'POST'])
def profile():
    if 'user_id' not in session:
        flash("Please log in.", "warning")
        return redirect(url_for('login'))

    user = User.query.get(session['user_id'])
    if request.method == 'POST':
        new_username = request.form.get('username', '').strip()
        if new_username and new_username != user.username:
            if User.query.filter_by(username=new_username).first():
                flash("Username already taken.", "danger")
            else:
                user.username = new_username
                db.session.commit()
                session['username'] = new_username
                flash("Profile updated successfully.", "success")
        return redirect(url_for('profile'))

    return render_template('profile.html', user=user)


# Logout route
@app.route('/logout')
def logout():
    session.clear()
    flash("Logged out successfully.", "success")
    return redirect(url_for('index'))

# Database Explorer route (admin only)
@app.route('/database_explorer')
def database_explorer():
    if 'user_id' not in session:
        flash("Please log in to access this page.", "warning")
        return redirect(url_for('login'))

    # For security, you might want to restrict this to specific users
    # This is a simple example - in production, add proper authorization

    tables = []

    # Get all tables using raw SQL with sqlite3
    import sqlite3
    conn = sqlite3.connect('instance/database.db')
    cursor = conn.cursor()

    # Get all tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    table_names = cursor.fetchall()

    for table_tuple in table_names:
        table_name = table_tuple[0]
        table_info = {
            'name': table_name,
            'schema': [],
            'data': []
        }

        # Get schema
        cursor.execute(f'PRAGMA table_info("{table_name}")')
        table_info['schema'] = cursor.fetchall()

        # Get data
        cursor.execute(f'SELECT * FROM "{table_name}"')
        table_info['data'] = cursor.fetchall()

        tables.append(table_info)

    conn.close()
    return render_template('database_view.html', tables=tables)

if __name__ == '__main__':
    app.run(debug=True)
