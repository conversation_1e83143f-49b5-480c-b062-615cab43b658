import sqlite3
import os
from tabulate import tabulate

# Connect to the database
conn = sqlite3.connect('instance/database.db')
cursor = conn.cursor()

# Get list of tables
cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = cursor.fetchall()

print("\n=== DATABASE TABLES ===")
for table in tables:
    table_name = table[0]
    print(f"\n--- TABLE: {table_name} ---")

    # Get table schema - use quotes around table name to handle reserved words
    cursor.execute(f'PRAGMA table_info("{table_name}")')
    columns = cursor.fetchall()
    print("\nSchema:")
    headers = ["Column ID", "Name", "Type", "NotNull", "Default", "PK"]
    print(tabulate([[col[0], col[1], col[2], col[3], col[4], col[5]] for col in columns], headers=headers))

    # Get table data - use quotes around table name
    cursor.execute(f'SELECT * FROM "{table_name}"')
    rows = cursor.fetchall()

    if rows:
        print(f"\nData ({len(rows)} rows):")
        headers = [col[1] for col in columns]
        print(tabulate(rows, headers=headers))
    else:
        print("\nNo data in this table.")

# Close the connection
conn.close()

print("\nDatabase exploration complete!")
