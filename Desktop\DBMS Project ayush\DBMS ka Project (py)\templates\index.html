<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Finance Tracker</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
    <!-- Theme Toggle Button -->
    <button class="theme-toggle" id="theme-toggle" title="Toggle dark/light mode">
        <i class="fas fa-moon"></i>
    </button>

    <!-- Hero Section -->
    <div class="hero-container">
        <div class="hero-content slide-up">
            <h1 class="hero-title">Personal Finance Tracker</h1>
            <p class="hero-subtitle">Take control of your finances with our simple and powerful tracking tool</p>

            <div class="hero-buttons">
                <a href="{{ url_for('login') }}" class="btn btn-primary">Login</a>
                <a href="{{ url_for('register') }}" class="btn btn-outline">Sign Up</a>
            </div>
        </div>

        <div class="hero-image fade-in">
            <img src="https://cdn-icons-png.flaticon.com/512/2037/2037397.png" alt="Finance Illustration">
        </div>
    </div>

    <!-- Features Section -->
    <div class="features-container">
        <h2 class="section-title">Why Choose Our Finance Tracker?</h2>

        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3 class="feature-title">Track Expenses</h3>
                <p class="feature-description">Easily record and categorize your daily expenses and income</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-pie"></i>
                </div>
                <h3 class="feature-title">Visualize Data</h3>
                <p class="feature-description">See where your money goes with intuitive charts and graphs</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h3 class="feature-title">Secure & Private</h3>
                <p class="feature-description">Your financial data stays private and secure</p>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="landing-footer">
        <p>&copy; 2023 Personal Finance Tracker. All rights reserved.</p>
    </footer>

    <script>
        // Theme Toggle Functionality
        const themeToggle = document.getElementById('theme-toggle');
        const themeIcon = themeToggle.querySelector('i');

        // Check for saved theme preference or default to light
        const currentTheme = localStorage.getItem('theme') || 'light';
        if (currentTheme === 'dark') {
            document.body.classList.add('dark-theme');
            themeIcon.classList.replace('fa-moon', 'fa-sun');
        }

        // Toggle theme when button is clicked
        themeToggle.addEventListener('click', () => {
            document.body.classList.toggle('dark-theme');

            // Update icon
            if (document.body.classList.contains('dark-theme')) {
                themeIcon.classList.replace('fa-moon', 'fa-sun');
                localStorage.setItem('theme', 'dark');
            } else {
                themeIcon.classList.replace('fa-sun', 'fa-moon');
                localStorage.setItem('theme', 'light');
            }
        });
    </script>
</body>
</html>
